/* tslint:disable */
/* eslint-disable */
/**
 * This file was automatically generated by Payload.
 * DO NOT MODIFY IT BY HAND. Instead, modify your source Payload config,
 * and re-run `payload generate:types` to regenerate this file.
 */

/**
 * Supported timezones in IANA format.
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "supportedTimezones".
 */
export type SupportedTimezones =
  | 'Pacific/Midway'
  | 'Pacific/Niue'
  | 'Pacific/Honolulu'
  | 'Pacific/Rarotonga'
  | 'America/Anchorage'
  | 'Pacific/Gambier'
  | 'America/Los_Angeles'
  | 'America/Tijuana'
  | 'America/Denver'
  | 'America/Phoenix'
  | 'America/Chicago'
  | 'America/Guatemala'
  | 'America/New_York'
  | 'America/Bogota'
  | 'America/Caracas'
  | 'America/Santiago'
  | 'America/Buenos_Aires'
  | 'America/Sao_Paulo'
  | 'Atlantic/South_Georgia'
  | 'Atlantic/Azores'
  | 'Atlantic/Cape_Verde'
  | 'Europe/London'
  | 'Europe/Berlin'
  | 'Africa/Lagos'
  | 'Europe/Athens'
  | 'Africa/Cairo'
  | 'Europe/Moscow'
  | 'Asia/Riyadh'
  | 'Asia/Dubai'
  | 'Asia/Baku'
  | 'Asia/Karachi'
  | 'Asia/Tashkent'
  | 'Asia/Calcutta'
  | 'Asia/Dhaka'
  | 'Asia/Almaty'
  | 'Asia/Jakarta'
  | 'Asia/Bangkok'
  | 'Asia/Shanghai'
  | 'Asia/Singapore'
  | 'Asia/Tokyo'
  | 'Asia/Seoul'
  | 'Australia/Brisbane'
  | 'Australia/Sydney'
  | 'Pacific/Guam'
  | 'Pacific/Noumea'
  | 'Pacific/Auckland'
  | 'Pacific/Fiji';

export interface Config {
  auth: {
    users: UserAuthOperations;
  };
  blocks: {};
  collections: {
    users: User;
    media: Media;
    downloads: Download;
    posts: Post;
    documentation: Documentation;
    private: Private;
    courses: Course;
    course_lessons: CourseLesson;
    course_quizzes: CourseQuizz;
    quiz_questions: QuizQuestion;
    survey_questions: SurveyQuestion;
    surveys: Survey;
    'payload-locked-documents': PayloadLockedDocument;
    'payload-preferences': PayloadPreference;
    'payload-migrations': PayloadMigration;
  };
  collectionsJoins: {};
  collectionsSelect: {
    users: UsersSelect<false> | UsersSelect<true>;
    media: MediaSelect<false> | MediaSelect<true>;
    downloads: DownloadsSelect<false> | DownloadsSelect<true>;
    posts: PostsSelect<false> | PostsSelect<true>;
    documentation: DocumentationSelect<false> | DocumentationSelect<true>;
    private: PrivateSelect<false> | PrivateSelect<true>;
    courses: CoursesSelect<false> | CoursesSelect<true>;
    course_lessons: CourseLessonsSelect<false> | CourseLessonsSelect<true>;
    course_quizzes: CourseQuizzesSelect<false> | CourseQuizzesSelect<true>;
    quiz_questions: QuizQuestionsSelect<false> | QuizQuestionsSelect<true>;
    survey_questions: SurveyQuestionsSelect<false> | SurveyQuestionsSelect<true>;
    surveys: SurveysSelect<false> | SurveysSelect<true>;
    'payload-locked-documents': PayloadLockedDocumentsSelect<false> | PayloadLockedDocumentsSelect<true>;
    'payload-preferences': PayloadPreferencesSelect<false> | PayloadPreferencesSelect<true>;
    'payload-migrations': PayloadMigrationsSelect<false> | PayloadMigrationsSelect<true>;
  };
  db: {
    defaultIDType: string;
  };
  globals: {};
  globalsSelect: {};
  locale: null;
  user: User & {
    collection: 'users';
  };
  jobs: {
    tasks: unknown;
    workflows: unknown;
  };
}
export interface UserAuthOperations {
  forgotPassword: {
    email: string;
    password: string;
  };
  login: {
    email: string;
    password: string;
  };
  registerFirstUser: {
    email: string;
    password: string;
  };
  unlock: {
    email: string;
    password: string;
  };
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users".
 */
export interface User {
  id: string;
  name?: string | null;
  role: 'admin' | 'user';
  updatedAt: string;
  createdAt: string;
  email: string;
  resetPasswordToken?: string | null;
  resetPasswordExpiration?: string | null;
  salt?: string | null;
  hash?: string | null;
  loginAttempts?: number | null;
  lockUntil?: string | null;
  password?: string | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media".
 */
export interface Media {
  id: string;
  /**
   * Alternative text for accessibility and SEO
   */
  alt: string;
  /**
   * Optional caption for the media
   */
  caption?: string | null;
  /**
   * Type of media file
   */
  type?: ('image' | 'video' | 'document') | null;
  /**
   * Tags for organizing and searching media
   */
  tags?:
    | {
        tag?: string | null;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
  url?: string | null;
  thumbnailURL?: string | null;
  filename?: string | null;
  mimeType?: string | null;
  filesize?: number | null;
  width?: number | null;
  height?: number | null;
  focalX?: number | null;
  focalY?: number | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "downloads".
 */
export interface Download {
  id: string;
  /**
   * Display name for the download file
   */
  title: string;
  /**
   * Description of what this file contains
   */
  description?: string | null;
  /**
   * Category of the download file
   */
  category?: ('document' | 'template' | 'resource' | 'software' | 'media' | 'archive' | 'other') | null;
  /**
   * Tags for organizing and searching downloads
   */
  tags?:
    | {
        tag?: string | null;
        id?: string | null;
      }[]
    | null;
  /**
   * Number of times this file has been downloaded
   */
  downloadCount?: number | null;
  /**
   * Mark as featured download
   */
  featured?: boolean | null;
  /**
   * Who can access this download
   */
  accessLevel?: ('public' | 'registered' | 'premium') | null;
  updatedAt: string;
  createdAt: string;
  url?: string | null;
  thumbnailURL?: string | null;
  filename?: string | null;
  mimeType?: string | null;
  filesize?: number | null;
  width?: number | null;
  height?: number | null;
  focalX?: number | null;
  focalY?: number | null;
}
/**
 * Blog posts for the website
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "posts".
 */
export interface Post {
  id: string;
  title: string;
  /**
   * The URL-friendly identifier for this post
   */
  slug: string;
  /**
   * A brief summary of the post
   */
  description?: string | null;
  /**
   * The main content of the blog post
   */
  content: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  /**
   * The date and time this post was published
   */
  publishedAt?: string | null;
  /**
   * Featured image for the blog post
   */
  image_id?: (string | null) | Media;
  /**
   * Only published posts will be visible on the website
   */
  status: 'draft' | 'published';
  /**
   * Categories for this post
   */
  categories?:
    | {
        category?: string | null;
        id?: string | null;
      }[]
    | null;
  /**
   * Tags for this post
   */
  tags?:
    | {
        tag?: string | null;
        id?: string | null;
      }[]
    | null;
  /**
   * Files for download in this post
   */
  downloads?: (string | Download)[] | null;
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * Documentation content for the application
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "documentation".
 */
export interface Documentation {
  id: string;
  title: string;
  /**
   * The URL-friendly identifier for this document
   */
  slug: string;
  description?: string | null;
  /**
   * The main content of the documentation
   */
  content?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  /**
   * Files for download in this documentation
   */
  downloads?: (string | Download)[] | null;
  publishedAt?: string | null;
  status: 'draft' | 'published';
  order?: number | null;
  categories?:
    | {
        category?: string | null;
        id?: string | null;
      }[]
    | null;
  tags?:
    | {
        tag?: string | null;
        id?: string | null;
      }[]
    | null;
  parent?: (string | null) | Documentation;
  breadcrumbs?:
    | {
        doc?: (string | null) | Documentation;
        url?: string | null;
        label?: string | null;
        id?: string | null;
      }[]
    | null;
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * Private posts that are not indexed by search engines
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "private".
 */
export interface Private {
  id: string;
  title: string;
  /**
   * The URL-friendly identifier for this private post
   */
  slug: string;
  /**
   * A brief summary of the private post
   */
  description?: string | null;
  /**
   * The main content of the private post
   */
  content: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  };
  /**
   * The date and time this post was published
   */
  publishedAt?: string | null;
  /**
   * Featured image for the private post
   */
  image_id?: (string | null) | Download;
  /**
   * Featured image (larger version) for the private post
   */
  featured_image_id?: (string | null) | Download;
  /**
   * Only published posts will be visible on the website
   */
  status: 'draft' | 'published';
  /**
   * Categories for this private post
   */
  categories?:
    | {
        category?: string | null;
        id?: string | null;
      }[]
    | null;
  /**
   * Tags for this private post
   */
  tags?:
    | {
        tag?: string | null;
        id?: string | null;
      }[]
    | null;
  /**
   * Files for download in this private post
   */
  downloads?: (string | Download)[] | null;
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * Courses in the learning management system
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "courses".
 */
export interface Course {
  id: string;
  title: string;
  /**
   * The URL-friendly identifier for this course
   */
  slug: string;
  description?: string | null;
  content?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  /**
   * Files for download in this course
   */
  downloads?: (string | Download)[] | null;
  publishedAt?: string | null;
  status: 'draft' | 'published';
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * Lessons for courses in the learning management system
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "course_lessons".
 */
export interface CourseLesson {
  id: string;
  title: string;
  /**
   * Video ID from Bunny.net (if this lesson includes a video)
   */
  bunny_video_id?: string | null;
  /**
   * Library ID from Bunny.net (defaults to main library)
   */
  bunny_library_id?: string | null;
  /**
   * Source platform for the external video
   */
  video_source_type?: ('youtube' | 'vimeo') | null;
  /**
   * Video ID from YouTube or Vimeo (if this lesson includes an external video)
   */
  youtube_video_id?: string | null;
  todo_complete_quiz?: boolean | null;
  /**
   * The URL-friendly identifier for this lesson
   */
  slug: string;
  description?: string | null;
  content?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  /**
   * Order in which this lesson appears in the course
   */
  lesson_number: number;
  estimated_duration?: number | null;
  course_id?: (string | null) | Course;
  /**
   * The quiz associated with this lesson (if any)
   */
  quiz_id?: (string | null) | CourseQuizz;
  /**
   * The survey associated with this lesson (if any)
   */
  survey_id?: (string | null) | Survey;
  /**
   * Files for download in this lesson
   */
  downloads?: (string | Download)[] | null;
  publishedAt?: string | null;
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * Quizzes for courses in the learning management system
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "course_quizzes".
 */
export interface CourseQuizz {
  id: string;
  title: string;
  /**
   * The URL-friendly identifier for this quiz
   */
  slug: string;
  description?: string | null;
  course_id?: (string | null) | Course;
  /**
   * Percentage required to pass the quiz
   */
  pass_threshold?: number | null;
  /**
   * Questions included in this quiz
   */
  questions?: (string | QuizQuestion)[] | null;
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * Questions for course quizzes
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "quiz_questions".
 */
export interface QuizQuestion {
  id: string;
  question: string;
  type: 'multiple_choice';
  /**
   * Auto-generated unique slug for API lookups, derived from question text.
   */
  questionSlug: string;
  options: {
    text: string;
    isCorrect?: boolean | null;
    id?: string | null;
  }[];
  /**
   * Optional explanation for this question (Lexical Rich Text)
   */
  explanation?: {
    root: {
      type: string;
      children: {
        type: string;
        version: number;
        [k: string]: unknown;
      }[];
      direction: ('ltr' | 'rtl') | null;
      format: 'left' | 'start' | 'center' | 'right' | 'end' | 'justify' | '';
      indent: number;
      version: number;
    };
    [k: string]: unknown;
  } | null;
  /**
   * Order within the quiz (lower numbers appear first)
   */
  order?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * Surveys for user assessment and feedback
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "surveys".
 */
export interface Survey {
  id: string;
  /**
   * The URL-friendly identifier for this survey
   */
  slug: string;
  /**
   * A brief summary of the survey
   */
  description?: string | null;
  /**
   * Only published surveys will be visible to users
   */
  status: 'draft' | 'published';
  /**
   * The date and time this survey was published
   */
  publishedAt?: string | null;
  /**
   * Files for download in this survey
   */
  downloads?: (string | Download)[] | null;
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * Questions for surveys
 *
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "survey_questions".
 */
export interface SurveyQuestion {
  id: string;
  /**
   * Unique identifier for the survey question
   */
  questionSlug: string;
  /**
   * The question text
   */
  text: string;
  /**
   * The type of question
   */
  type: 'multiple_choice' | 'text_field' | 'scale';
  /**
   * Additional context or instructions for the question
   */
  description?: string | null;
  /**
   * Whether this question requires an answer
   */
  required?: boolean | null;
  /**
   * Answer options for this question
   */
  options?:
    | {
        option: string;
        id?: string | null;
      }[]
    | null;
  /**
   * The category this question belongs to (e.g., Structure, Story, Style)
   */
  category: string;
  /**
   * Whether a high score is positive or negative for this question
   */
  questionspin: 'Positive' | 'Negative';
  /**
   * Position in the survey (lower numbers appear first)
   */
  position?: number | null;
  updatedAt: string;
  createdAt: string;
  _status?: ('draft' | 'published') | null;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents".
 */
export interface PayloadLockedDocument {
  id: string;
  document?:
    | ({
        relationTo: 'users';
        value: string | User;
      } | null)
    | ({
        relationTo: 'media';
        value: string | Media;
      } | null)
    | ({
        relationTo: 'downloads';
        value: string | Download;
      } | null)
    | ({
        relationTo: 'posts';
        value: string | Post;
      } | null)
    | ({
        relationTo: 'documentation';
        value: string | Documentation;
      } | null)
    | ({
        relationTo: 'private';
        value: string | Private;
      } | null)
    | ({
        relationTo: 'courses';
        value: string | Course;
      } | null)
    | ({
        relationTo: 'course_lessons';
        value: string | CourseLesson;
      } | null)
    | ({
        relationTo: 'course_quizzes';
        value: string | CourseQuizz;
      } | null)
    | ({
        relationTo: 'quiz_questions';
        value: string | QuizQuestion;
      } | null)
    | ({
        relationTo: 'survey_questions';
        value: string | SurveyQuestion;
      } | null)
    | ({
        relationTo: 'surveys';
        value: string | Survey;
      } | null);
  globalSlug?: string | null;
  user: {
    relationTo: 'users';
    value: string | User;
  };
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences".
 */
export interface PayloadPreference {
  id: string;
  user: {
    relationTo: 'users';
    value: string | User;
  };
  key?: string | null;
  value?:
    | {
        [k: string]: unknown;
      }
    | unknown[]
    | string
    | number
    | boolean
    | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations".
 */
export interface PayloadMigration {
  id: string;
  name?: string | null;
  batch?: number | null;
  updatedAt: string;
  createdAt: string;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "users_select".
 */
export interface UsersSelect<T extends boolean = true> {
  name?: T;
  role?: T;
  updatedAt?: T;
  createdAt?: T;
  email?: T;
  resetPasswordToken?: T;
  resetPasswordExpiration?: T;
  salt?: T;
  hash?: T;
  loginAttempts?: T;
  lockUntil?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "media_select".
 */
export interface MediaSelect<T extends boolean = true> {
  alt?: T;
  caption?: T;
  type?: T;
  tags?:
    | T
    | {
        tag?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  url?: T;
  thumbnailURL?: T;
  filename?: T;
  mimeType?: T;
  filesize?: T;
  width?: T;
  height?: T;
  focalX?: T;
  focalY?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "downloads_select".
 */
export interface DownloadsSelect<T extends boolean = true> {
  title?: T;
  description?: T;
  category?: T;
  tags?:
    | T
    | {
        tag?: T;
        id?: T;
      };
  downloadCount?: T;
  featured?: T;
  accessLevel?: T;
  updatedAt?: T;
  createdAt?: T;
  url?: T;
  thumbnailURL?: T;
  filename?: T;
  mimeType?: T;
  filesize?: T;
  width?: T;
  height?: T;
  focalX?: T;
  focalY?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "posts_select".
 */
export interface PostsSelect<T extends boolean = true> {
  title?: T;
  slug?: T;
  description?: T;
  content?: T;
  publishedAt?: T;
  image_id?: T;
  status?: T;
  categories?:
    | T
    | {
        category?: T;
        id?: T;
      };
  tags?:
    | T
    | {
        tag?: T;
        id?: T;
      };
  downloads?: T;
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "documentation_select".
 */
export interface DocumentationSelect<T extends boolean = true> {
  title?: T;
  slug?: T;
  description?: T;
  content?: T;
  downloads?: T;
  publishedAt?: T;
  status?: T;
  order?: T;
  categories?:
    | T
    | {
        category?: T;
        id?: T;
      };
  tags?:
    | T
    | {
        tag?: T;
        id?: T;
      };
  parent?: T;
  breadcrumbs?:
    | T
    | {
        doc?: T;
        url?: T;
        label?: T;
        id?: T;
      };
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "private_select".
 */
export interface PrivateSelect<T extends boolean = true> {
  title?: T;
  slug?: T;
  description?: T;
  content?: T;
  publishedAt?: T;
  image_id?: T;
  featured_image_id?: T;
  status?: T;
  categories?:
    | T
    | {
        category?: T;
        id?: T;
      };
  tags?:
    | T
    | {
        tag?: T;
        id?: T;
      };
  downloads?: T;
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "courses_select".
 */
export interface CoursesSelect<T extends boolean = true> {
  title?: T;
  slug?: T;
  description?: T;
  content?: T;
  downloads?: T;
  publishedAt?: T;
  status?: T;
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "course_lessons_select".
 */
export interface CourseLessonsSelect<T extends boolean = true> {
  title?: T;
  bunny_video_id?: T;
  bunny_library_id?: T;
  video_source_type?: T;
  youtube_video_id?: T;
  todo_complete_quiz?: T;
  slug?: T;
  description?: T;
  content?: T;
  lesson_number?: T;
  estimated_duration?: T;
  course_id?: T;
  quiz_id?: T;
  survey_id?: T;
  downloads?: T;
  publishedAt?: T;
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "course_quizzes_select".
 */
export interface CourseQuizzesSelect<T extends boolean = true> {
  title?: T;
  slug?: T;
  description?: T;
  course_id?: T;
  pass_threshold?: T;
  questions?: T;
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "quiz_questions_select".
 */
export interface QuizQuestionsSelect<T extends boolean = true> {
  question?: T;
  type?: T;
  questionSlug?: T;
  options?:
    | T
    | {
        text?: T;
        isCorrect?: T;
        id?: T;
      };
  explanation?: T;
  order?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "survey_questions_select".
 */
export interface SurveyQuestionsSelect<T extends boolean = true> {
  questionSlug?: T;
  text?: T;
  type?: T;
  description?: T;
  required?: T;
  options?:
    | T
    | {
        option?: T;
        id?: T;
      };
  category?: T;
  questionspin?: T;
  position?: T;
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "surveys_select".
 */
export interface SurveysSelect<T extends boolean = true> {
  slug?: T;
  description?: T;
  status?: T;
  publishedAt?: T;
  downloads?: T;
  updatedAt?: T;
  createdAt?: T;
  _status?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-locked-documents_select".
 */
export interface PayloadLockedDocumentsSelect<T extends boolean = true> {
  document?: T;
  globalSlug?: T;
  user?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-preferences_select".
 */
export interface PayloadPreferencesSelect<T extends boolean = true> {
  user?: T;
  key?: T;
  value?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "payload-migrations_select".
 */
export interface PayloadMigrationsSelect<T extends boolean = true> {
  name?: T;
  batch?: T;
  updatedAt?: T;
  createdAt?: T;
}
/**
 * This interface was referenced by `Config`'s JSON-Schema
 * via the `definition` "auth".
 */
export interface Auth {
  [k: string]: unknown;
}


declare module 'payload' {
  export interface GeneratedTypes extends Config {}
}